package com.wosai.pay.common.state.manager.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcMethod;
import com.wosai.pay.common.state.manager.jsonrpc.request.StateQueryRequest;
import com.wosai.pay.common.state.manager.jsonrpc.request.StateUpdateRequest;
import com.wosai.pay.common.state.manager.request.OperationLogRequest;
import com.wosai.pay.common.state.manager.result.StateManagerResult;

/**
 * 状态管理 JSON-RPC 服务接口
 * 提供统一的状态查询和修改接口
 *
 * <AUTHOR>
 * @since 1.2.7-SNAPSHOT
 */
public interface StateManagerJsonRpcService {

    /**
     * 查询状态
     *
     * @param request 状态查询请求
     * @return 查询结果
     */
    @JsonRpcMethod("queryState")
    StateManagerResult queryState(StateQueryRequest request);

    /**
     * 修改状态
     *
     * @param request 状态修改请求
     * @return 修改结果
     */
    @JsonRpcMethod("changeState")
    Boolean changeState(StateUpdateRequest request, OperationLogRequest operationLogRequest);
}
