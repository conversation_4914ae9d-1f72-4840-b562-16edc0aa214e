package com.wosai.pay.common.state.manager.registry;

import com.wosai.pay.common.data.Criteria;
import com.wosai.pay.common.state.manager.domain.AbstractStateDO;
import com.wosai.pay.common.state.manager.request.AbstractEntity;
import com.wosai.pay.common.state.manager.request.OperationLogRequest;
import com.wosai.pay.common.state.manager.result.StateManagerResult;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * 状态处理器接口
 * 不同类型可以实现此接口来自定义状态处理逻辑
 */
public interface StateManagerProcessor<T extends AbstractEntity,E extends AbstractStateDO, R extends StateManagerResult> {

    /**
     * 获取此处理器支持的实体类型
     * 默认实现优先级：
     * 1. 检查@SupportedEntityType注解
     * 2. 通过反射自动获取泛型T的entityType()方法返回值
     * 3. 子类可以重写此方法提供自定义实现
     * @return 实体类型
     */
    default String getSupportedEntityType() {
        Class<?> currentClass = this.getClass();

        // 1. 优先检查@SupportedEntityType注解
        SupportedEntityType annotation = currentClass.getAnnotation(SupportedEntityType.class);
        if (annotation != null) {
            return annotation.value();
        }

        // 2. 通过反射获取泛型参数
        try {
            Type[] genericInterfaces = currentClass.getGenericInterfaces();

            for (Type genericInterface : genericInterfaces) {
                if (genericInterface instanceof ParameterizedType) {
                    ParameterizedType parameterizedType = (ParameterizedType) genericInterface;
                    if (parameterizedType.getRawType().equals(StateManagerProcessor.class)) {
                        // 获取第一个泛型参数T的实际类型
                        Type actualTypeArgument = parameterizedType.getActualTypeArguments()[0];
                        if (actualTypeArgument instanceof Class) {
                            @SuppressWarnings("unchecked")
                            Class<? extends AbstractEntity> entityClass = (Class<? extends AbstractEntity>) actualTypeArgument;
                            // 调用实体类的entityType()静态方法
                            Method entityTypeMethod = entityClass.getMethod("entityType");
                            return (String) entityTypeMethod.invoke(null);
                        }
                    }
                }
            }

            // 如果无法通过反射获取，抛出异常提示子类重写此方法
            throw new UnsupportedOperationException(
                "无法自动获取实体类型，请在 " + currentClass.getSimpleName() +
                " 中使用 @SupportedEntityType 注解或重写 getSupportedEntityType() 方法");

        } catch (Exception e) {
            throw new RuntimeException("获取支持的实体类型失败: " + e.getMessage(), e);
        }
    }


    Criteria  generateCriteria(T entity);


    E buildStateDO(T entity);


    /**
     * 创建状态查询结果实例
     * @param entity 状态管理实体
     * @return 状态查询结果
     */
    R createResultInstance(T entity);

    /**
     * 在状态更新后执行的操作
     * @param entity 状态更新请求
     * @param previousState 修改前状态
     * @param currentState 修改后状态
     */
     void afterStateChange(T entity, R previousState, R currentState, OperationLogRequest operationLogRequest);
}