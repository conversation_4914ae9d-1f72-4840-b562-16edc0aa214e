package com.wosai.pay.common.state.manager.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.googlecode.jsonrpc4j.spring.JsonServiceExporter;
import com.wosai.pay.common.base.util.MapUtil;
import com.wosai.pay.common.state.manager.dao.StateConfigDao;
import com.wosai.pay.common.state.manager.dao.StateDao;
import com.wosai.pay.common.state.manager.service.RpcBusinessOpLogService;
import com.wosai.pay.common.state.manager.jsonrpc.StateManagerJsonRpcService;
import com.wosai.pay.common.state.manager.jsonrpc.StateManagerJsonRpcServiceImpl;
import com.wosai.pay.common.state.manager.request.AbstractEntity;
import com.wosai.pay.common.state.manager.service.StateManagerConfig;
import com.wosai.pay.common.state.manager.service.StateManagerService;
import com.wosai.pay.common.state.manager.service.StateManagerServiceImpl;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;

import javax.annotation.Resource;
import java.util.Map;

@Configuration
public class StateManagerAutoConfiguration {

    @Resource
    private StateManagerProperties stateManagerProperties;


    @Bean
    public StateConfigDao stateConfigDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        return new StateConfigDao(namedParameterJdbcTemplate);
    }

    @Bean
    public StateDao StateDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        return new StateDao(stateManagerProperties.getStateTableName(),namedParameterJdbcTemplate);
    }

    @Bean
    public StateManagerConfig stateManagerConfig(StateConfigDao stateDao) {
        return  new StateManagerConfig(stateDao,stateManagerProperties.getRefreshIntervalSeconds(),stateManagerProperties.getEnableAutoRefresh());
    }

    @Bean
    public StateManagerService stateManagerService(StateDao stateDao,
                                                   KafkaTemplate<String, Object> kafkaTemplate,
                                                   RpcBusinessOpLogService businessOpLogService) {
        return new StateManagerServiceImpl(stateDao, kafkaTemplate, kafkaTemplate, businessOpLogService);
    }

    @Bean
    public KafkaTemplate<String, Object> stateManagerKafkaTemplate() {
        Map<String, Object> configs = MapUtil.hashMap(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, stateManagerProperties.getBootstrapServers(),
                ProducerConfig.ACKS_CONFIG, "1",
                ProducerConfig.BATCH_SIZE_CONFIG, "16384",
                ProducerConfig.LINGER_MS_CONFIG, "1000",
                ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, "30000",
                ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class,
                ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class
        );
        return new KafkaTemplate<>(new DefaultKafkaProducerFactory<>(configs));
    }

    @Bean
    public StateManagerJsonRpcService stateManagerJsonRpcService() {
        return new StateManagerJsonRpcServiceImpl();
    }

    @Bean("/state_manager")
    public JsonServiceExporter stateManagerJsonRpcServiceExporter(StateManagerJsonRpcService service) {
        JsonServiceExporter exporter = new JsonServiceExporter();
        exporter.setService(service);
        exporter.setServiceInterface(StateManagerJsonRpcService.class);
        ObjectMapper objectMapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        module.addDeserializer(AbstractEntity.class, new AbstractEntity.AbstractStateObjectDeserializer());
        objectMapper.registerModule(module);
        objectMapper.configure( DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        exporter.setObjectMapper(objectMapper);
        return exporter;
    }

    @Bean
    public JsonProxyFactoryBean  jsonProxyFactoryBean() {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(stateManagerProperties.getBusinessLogStashUrl() + "/rpc/businessOpLog");
        jsonProxyFactoryBean.setServiceInterface(RpcBusinessOpLogService.class);
        jsonProxyFactoryBean.setServerName("business-logstash");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);
        return jsonProxyFactoryBean;
    }


}
