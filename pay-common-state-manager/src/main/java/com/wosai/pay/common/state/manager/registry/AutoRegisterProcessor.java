package com.wosai.pay.common.state.manager.registry;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自动注册StateManagerProcessor的注解
 * 标记了此注解的处理器类将在应用启动时自动注册到StateManagerProcessorRegistry中
 * 
 * 使用示例：
 * @AutoRegisterProcessor
 * public class ProviderMchIdProcessor implements StateManagerProcessor<...> {
 *     // 处理器实现
 * }
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface AutoRegisterProcessor {
    
    /**
     * 处理器的优先级，数值越小优先级越高
     * 当多个处理器支持同一实体类型时，优先级高的会覆盖优先级低的
     * @return 优先级值，默认为0
     */
    int priority() default 0;
    
    /**
     * 是否启用自动注册，默认为true
     * @return 是否启用
     */
    boolean enabled() default true;
}
