package com.wosai.pay.common.state.manager.request;


import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;

public class ValidList<E> implements List<E> {
    private  List<E> list = new ArrayList();

    public static <E> ValidList<E> createInstance(List<E> list) {
        ValidList validList = new ValidList();
        validList.setList(list);
        return validList;
    }

    public List<E> getList() {
        return this.list;
    }

    public void setList(List<E> list) {
        this.list = list;
    }

    public int size() {
        return this.list.size();
    }

    public boolean isEmpty() {
        return this.list.isEmpty();
    }

    public boolean contains(Object o) {
        return this.list.contains(o);
    }

    public Iterator<E> iterator() {
        return this.list.iterator();
    }

    public Object[] toArray() {
        return this.list.toArray();
    }

    public <T> T[] toArray(T[] a) {
        return (T[])this.list.toArray(a);
    }

    public boolean add(E e) {
        return this.list.add(e);
    }

    public boolean remove(Object o) {
        return this.list.remove(o);
    }

    public boolean containsAll(Collection<?> c) {
        return this.list.contains(c);
    }

    public boolean addAll(Collection<? extends E> c) {
        return this.list.addAll(c);
    }

    public boolean addAll(int index, Collection<? extends E> c) {
        return this.list.addAll(index, c);
    }

    public boolean removeAll(Collection<?> c) {
        return this.list.removeAll(c);
    }

    public boolean retainAll(Collection<?> c) {
        return this.list.retainAll(c);
    }

    public void clear() {
        this.list.clear();
    }

    public E get(int index) {
        return (E)this.list.get(index);
    }

    public E set(int index, E element) {
        return (E)this.list.set(index, element);
    }

    public void add(int index, E element) {
        this.list.add(index, element);
    }

    public E remove(int index) {
        return (E)this.list.remove(index);
    }

    public int indexOf(Object o) {
        return this.list.indexOf(o);
    }

    public int lastIndexOf(Object o) {
        return this.list.lastIndexOf(o);
    }

    public ListIterator<E> listIterator() {
        return this.list.listIterator();
    }

    public ListIterator<E> listIterator(int index) {
        return this.list.listIterator(index);
    }

    public List<E> subList(int fromIndex, int toIndex) {
        return this.list.subList(fromIndex, toIndex);
    }
}
