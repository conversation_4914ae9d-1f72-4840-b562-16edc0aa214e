package com.wosai.pay.common.state.manager.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class StateManagerProperties {

    @Value("${pay.common.config.state.manager.state_table_name}")
    private String  stateTableName;

    @Value("${pay.common.config.state.manager,business_log_stash_url}")
    private String businessLogStashUrl;

    @Value("${pay.common.config.state.manager.kafka.bootstrap.servers}")
    private String bootstrapServers;

    @Value("${pay.common.config.state.manager.config.enable_auto_refresh:false}")
    private Boolean enableAutoRefresh;

    @Value("${pay.common.config.state.manager.config.refresh_interval_seconds:60}")
    private Long refreshIntervalSeconds;

    public String getStateTableName() {
        return stateTableName;
    }

    public void setStateTableName(String stateTableName) {
        this.stateTableName = stateTableName;
    }

    public String getBusinessLogStashUrl() {
        return businessLogStashUrl;
    }

    public void setBusinessLogStashUrl(String businessLogStashUrl) {
        this.businessLogStashUrl = businessLogStashUrl;
    }

    public String getBootstrapServers() {
        return bootstrapServers;
    }

    public void setBootstrapServers(String bootstrapServers) {
        this.bootstrapServers = bootstrapServers;
    }

    public Boolean getEnableAutoRefresh() {
        return enableAutoRefresh;
    }

    public void setEnableAutoRefresh(Boolean enableAutoRefresh) {
        this.enableAutoRefresh = enableAutoRefresh;
    }

    public Long getRefreshIntervalSeconds() {
        return refreshIntervalSeconds;
    }

    public void setRefreshIntervalSeconds(Long refreshIntervalSeconds) {
        this.refreshIntervalSeconds = refreshIntervalSeconds;
    }
}
