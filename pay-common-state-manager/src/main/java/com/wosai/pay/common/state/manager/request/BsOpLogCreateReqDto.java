package com.wosai.pay.common.state.manager.request;

import com.fasterxml.jackson.annotation.JsonProperty;


public class BsOpLogCreateReqDto {
    @JsonProperty("outer_scene_trace_id")
    String outerSceneTraceId;
    @JsonProperty("log_template_code")
   String logTemplateCode;
    @JsonProperty("platform_code")
    private String platformCode;
    @JsonProperty("root_object_id")
    String rootObjectId;
    @JsonProperty("op_object_id")
   String opObjectId;
    @JsonProperty("diff_list")
   ValidList<Diff> diffList;
    @JsonProperty("op_user_id")
    String opUserId;
    @JsonProperty("op_user_name")
    String opUserName;
    @JsonProperty("same_save_column_code")
    String sameSaveColumnCode;
    String remark;
    Long ctime;

    private static Long $default$ctime() {
        return System.currentTimeMillis();
    }

    public static BsOpLogCreateReqDtoBuilder builder() {
        return new BsOpLogCreateReqDtoBuilder();
    }

    public String getOuterSceneTraceId() {
        return this.outerSceneTraceId;
    }

    public String getLogTemplateCode() {
        return this.logTemplateCode;
    }

    public String getPlatformCode() {
        return this.platformCode;
    }

    public String getRootObjectId() {
        return this.rootObjectId;
    }

    public String getOpObjectId() {
        return this.opObjectId;
    }

    public ValidList<Diff> getDiffList() {
        return this.diffList;
    }

    public String getOpUserId() {
        return this.opUserId;
    }

    public String getOpUserName() {
        return this.opUserName;
    }

    public String getSameSaveColumnCode() {
        return this.sameSaveColumnCode;
    }

    public String getRemark() {
        return this.remark;
    }

    public Long getCtime() {
        return this.ctime;
    }

    @JsonProperty("outer_scene_trace_id")
    public void setOuterSceneTraceId(String outerSceneTraceId) {
        this.outerSceneTraceId = outerSceneTraceId;
    }

    @JsonProperty("log_template_code")
    public void setLogTemplateCode(String logTemplateCode) {
        this.logTemplateCode = logTemplateCode;
    }

    @JsonProperty("platform_code")
    public void setPlatformCode(String platformCode) {
        this.platformCode = platformCode;
    }

    @JsonProperty("root_object_id")
    public void setRootObjectId(String rootObjectId) {
        this.rootObjectId = rootObjectId;
    }

    @JsonProperty("op_object_id")
    public void setOpObjectId(String opObjectId) {
        this.opObjectId = opObjectId;
    }

    @JsonProperty("diff_list")
    public void setDiffList(ValidList<Diff> diffList) {
        this.diffList = diffList;
    }

    @JsonProperty("op_user_id")
    public void setOpUserId(String opUserId) {
        this.opUserId = opUserId;
    }

    @JsonProperty("op_user_name")
    public void setOpUserName(String opUserName) {
        this.opUserName = opUserName;
    }

    @JsonProperty("same_save_column_code")
    public void setSameSaveColumnCode(String sameSaveColumnCode) {
        this.sameSaveColumnCode = sameSaveColumnCode;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof BsOpLogCreateReqDto)) {
            return false;
        } else {
            BsOpLogCreateReqDto other = (BsOpLogCreateReqDto)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$outerSceneTraceId = this.getOuterSceneTraceId();
                Object other$outerSceneTraceId = other.getOuterSceneTraceId();
                if (this$outerSceneTraceId == null) {
                    if (other$outerSceneTraceId != null) {
                        return false;
                    }
                } else if (!this$outerSceneTraceId.equals(other$outerSceneTraceId)) {
                    return false;
                }

                Object this$logTemplateCode = this.getLogTemplateCode();
                Object other$logTemplateCode = other.getLogTemplateCode();
                if (this$logTemplateCode == null) {
                    if (other$logTemplateCode != null) {
                        return false;
                    }
                } else if (!this$logTemplateCode.equals(other$logTemplateCode)) {
                    return false;
                }

                Object this$platformCode = this.getPlatformCode();
                Object other$platformCode = other.getPlatformCode();
                if (this$platformCode == null) {
                    if (other$platformCode != null) {
                        return false;
                    }
                } else if (!this$platformCode.equals(other$platformCode)) {
                    return false;
                }

                Object this$rootObjectId = this.getRootObjectId();
                Object other$rootObjectId = other.getRootObjectId();
                if (this$rootObjectId == null) {
                    if (other$rootObjectId != null) {
                        return false;
                    }
                } else if (!this$rootObjectId.equals(other$rootObjectId)) {
                    return false;
                }

                Object this$opObjectId = this.getOpObjectId();
                Object other$opObjectId = other.getOpObjectId();
                if (this$opObjectId == null) {
                    if (other$opObjectId != null) {
                        return false;
                    }
                } else if (!this$opObjectId.equals(other$opObjectId)) {
                    return false;
                }

                Object this$diffList = this.getDiffList();
                Object other$diffList = other.getDiffList();
                if (this$diffList == null) {
                    if (other$diffList != null) {
                        return false;
                    }
                } else if (!this$diffList.equals(other$diffList)) {
                    return false;
                }

                Object this$opUserId = this.getOpUserId();
                Object other$opUserId = other.getOpUserId();
                if (this$opUserId == null) {
                    if (other$opUserId != null) {
                        return false;
                    }
                } else if (!this$opUserId.equals(other$opUserId)) {
                    return false;
                }

                Object this$opUserName = this.getOpUserName();
                Object other$opUserName = other.getOpUserName();
                if (this$opUserName == null) {
                    if (other$opUserName != null) {
                        return false;
                    }
                } else if (!this$opUserName.equals(other$opUserName)) {
                    return false;
                }

                Object this$sameSaveColumnCode = this.getSameSaveColumnCode();
                Object other$sameSaveColumnCode = other.getSameSaveColumnCode();
                if (this$sameSaveColumnCode == null) {
                    if (other$sameSaveColumnCode != null) {
                        return false;
                    }
                } else if (!this$sameSaveColumnCode.equals(other$sameSaveColumnCode)) {
                    return false;
                }

                Object this$remark = this.getRemark();
                Object other$remark = other.getRemark();
                if (this$remark == null) {
                    if (other$remark != null) {
                        return false;
                    }
                } else if (!this$remark.equals(other$remark)) {
                    return false;
                }

                Object this$ctime = this.getCtime();
                Object other$ctime = other.getCtime();
                if (this$ctime == null) {
                    if (other$ctime != null) {
                        return false;
                    }
                } else if (!this$ctime.equals(other$ctime)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof BsOpLogCreateReqDto;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $outerSceneTraceId = this.getOuterSceneTraceId();
        result = result * 59 + ($outerSceneTraceId == null ? 43 : $outerSceneTraceId.hashCode());
        Object $logTemplateCode = this.getLogTemplateCode();
        result = result * 59 + ($logTemplateCode == null ? 43 : $logTemplateCode.hashCode());
        Object $platformCode = this.getPlatformCode();
        result = result * 59 + ($platformCode == null ? 43 : $platformCode.hashCode());
        Object $rootObjectId = this.getRootObjectId();
        result = result * 59 + ($rootObjectId == null ? 43 : $rootObjectId.hashCode());
        Object $opObjectId = this.getOpObjectId();
        result = result * 59 + ($opObjectId == null ? 43 : $opObjectId.hashCode());
        Object $diffList = this.getDiffList();
        result = result * 59 + ($diffList == null ? 43 : $diffList.hashCode());
        Object $opUserId = this.getOpUserId();
        result = result * 59 + ($opUserId == null ? 43 : $opUserId.hashCode());
        Object $opUserName = this.getOpUserName();
        result = result * 59 + ($opUserName == null ? 43 : $opUserName.hashCode());
        Object $sameSaveColumnCode = this.getSameSaveColumnCode();
        result = result * 59 + ($sameSaveColumnCode == null ? 43 : $sameSaveColumnCode.hashCode());
        Object $remark = this.getRemark();
        result = result * 59 + ($remark == null ? 43 : $remark.hashCode());
        Object $ctime = this.getCtime();
        result = result * 59 + ($ctime == null ? 43 : $ctime.hashCode());
        return result;
    }

    public String toString() {
        return "BsOpLogCreateReqDto(outerSceneTraceId=" + this.getOuterSceneTraceId() + ", logTemplateCode=" + this.getLogTemplateCode() + ", platformCode=" + this.getPlatformCode() + ", rootObjectId=" + this.getRootObjectId() + ", opObjectId=" + this.getOpObjectId() + ", diffList=" + this.getDiffList() + ", opUserId=" + this.getOpUserId() + ", opUserName=" + this.getOpUserName() + ", sameSaveColumnCode=" + this.getSameSaveColumnCode() + ", remark=" + this.getRemark() + ", ctime=" + this.getCtime() + ")";
    }

    public BsOpLogCreateReqDto(String outerSceneTraceId, String logTemplateCode, String platformCode, String rootObjectId, String opObjectId, ValidList<Diff> diffList, String opUserId, String opUserName, String sameSaveColumnCode, String remark, Long ctime) {
        this.outerSceneTraceId = outerSceneTraceId;
        this.logTemplateCode = logTemplateCode;
        this.platformCode = platformCode;
        this.rootObjectId = rootObjectId;
        this.opObjectId = opObjectId;
        this.diffList = diffList;
        this.opUserId = opUserId;
        this.opUserName = opUserName;
        this.sameSaveColumnCode = sameSaveColumnCode;
        this.remark = remark;
        this.ctime = ctime;
    }

    public BsOpLogCreateReqDto() {
        this.ctime = $default$ctime();
    }

    public static class BsOpLogCreateReqDtoBuilder {
        private String outerSceneTraceId;
        private String logTemplateCode;
        private String platformCode;
        private String rootObjectId;
        private String opObjectId;
        private ValidList<Diff> diffList;
        private String opUserId;
        private String opUserName;
        private String sameSaveColumnCode;
        private String remark;
        private boolean ctime$set;
        private Long ctime;

        BsOpLogCreateReqDtoBuilder() {
        }

        @JsonProperty("outer_scene_trace_id")
        public BsOpLogCreateReqDtoBuilder outerSceneTraceId(String outerSceneTraceId) {
            this.outerSceneTraceId = outerSceneTraceId;
            return this;
        }

        @JsonProperty("log_template_code")
        public BsOpLogCreateReqDtoBuilder logTemplateCode(String logTemplateCode) {
            this.logTemplateCode = logTemplateCode;
            return this;
        }

        @JsonProperty("platform_code")
        public BsOpLogCreateReqDtoBuilder platformCode(String platformCode) {
            this.platformCode = platformCode;
            return this;
        }

        @JsonProperty("root_object_id")
        public BsOpLogCreateReqDtoBuilder rootObjectId(String rootObjectId) {
            this.rootObjectId = rootObjectId;
            return this;
        }

        @JsonProperty("op_object_id")
        public BsOpLogCreateReqDtoBuilder opObjectId(String opObjectId) {
            this.opObjectId = opObjectId;
            return this;
        }

        @JsonProperty("diff_list")
        public BsOpLogCreateReqDtoBuilder diffList(ValidList<Diff> diffList) {
            this.diffList = diffList;
            return this;
        }

        @JsonProperty("op_user_id")
        public BsOpLogCreateReqDtoBuilder opUserId(String opUserId) {
            this.opUserId = opUserId;
            return this;
        }

        @JsonProperty("op_user_name")
        public BsOpLogCreateReqDtoBuilder opUserName(String opUserName) {
            this.opUserName = opUserName;
            return this;
        }

        @JsonProperty("same_save_column_code")
        public BsOpLogCreateReqDtoBuilder sameSaveColumnCode(String sameSaveColumnCode) {
            this.sameSaveColumnCode = sameSaveColumnCode;
            return this;
        }

        public BsOpLogCreateReqDtoBuilder remark(String remark) {
            this.remark = remark;
            return this;
        }

        public BsOpLogCreateReqDtoBuilder ctime(Long ctime) {
            this.ctime = ctime;
            this.ctime$set = true;
            return this;
        }

        public BsOpLogCreateReqDto build() {
            Long ctime = this.ctime;
            if (!this.ctime$set) {
                ctime = BsOpLogCreateReqDto.$default$ctime();
            }

            return new BsOpLogCreateReqDto(this.outerSceneTraceId, this.logTemplateCode, this.platformCode, this.rootObjectId, this.opObjectId, this.diffList, this.opUserId, this.opUserName, this.sameSaveColumnCode, this.remark, ctime);
        }

        public String toString() {
            return "BsOpLogCreateReqDto.BsOpLogCreateReqDtoBuilder(outerSceneTraceId=" + this.outerSceneTraceId + ", logTemplateCode=" + this.logTemplateCode + ", platformCode=" + this.platformCode + ", rootObjectId=" + this.rootObjectId + ", opObjectId=" + this.opObjectId + ", diffList=" + this.diffList + ", opUserId=" + this.opUserId + ", opUserName=" + this.opUserName + ", sameSaveColumnCode=" + this.sameSaveColumnCode + ", remark=" + this.remark + ", ctime=" + this.ctime + ")";
        }
    }

    public static class Diff {
        @JsonProperty("column_code")
       String columnCode;
        @JsonProperty("value_before")
       String valueBefore;
        @JsonProperty("value_after")
       String valueAfter;
        @JsonProperty("sensitive_type")
        String sensitiveType;

        public static DiffBuilder builder() {
            return new DiffBuilder();
        }

        public String getColumnCode() {
            return this.columnCode;
        }

        public String getValueBefore() {
            return this.valueBefore;
        }

        public String getValueAfter() {
            return this.valueAfter;
        }

        public String getSensitiveType() {
            return this.sensitiveType;
        }

        @JsonProperty("column_code")
        public void setColumnCode(String columnCode) {
            this.columnCode = columnCode;
        }

        @JsonProperty("value_before")
        public void setValueBefore(String valueBefore) {
            this.valueBefore = valueBefore;
        }

        @JsonProperty("value_after")
        public void setValueAfter(String valueAfter) {
            this.valueAfter = valueAfter;
        }

        @JsonProperty("sensitive_type")
        public void setSensitiveType(String sensitiveType) {
            this.sensitiveType = sensitiveType;
        }

        public boolean equals(Object o) {
            if (o == this) {
                return true;
            } else if (!(o instanceof Diff)) {
                return false;
            } else {
                Diff other = (Diff)o;
                if (!other.canEqual(this)) {
                    return false;
                } else {
                    Object this$columnCode = this.getColumnCode();
                    Object other$columnCode = other.getColumnCode();
                    if (this$columnCode == null) {
                        if (other$columnCode != null) {
                            return false;
                        }
                    } else if (!this$columnCode.equals(other$columnCode)) {
                        return false;
                    }

                    Object this$valueBefore = this.getValueBefore();
                    Object other$valueBefore = other.getValueBefore();
                    if (this$valueBefore == null) {
                        if (other$valueBefore != null) {
                            return false;
                        }
                    } else if (!this$valueBefore.equals(other$valueBefore)) {
                        return false;
                    }

                    Object this$valueAfter = this.getValueAfter();
                    Object other$valueAfter = other.getValueAfter();
                    if (this$valueAfter == null) {
                        if (other$valueAfter != null) {
                            return false;
                        }
                    } else if (!this$valueAfter.equals(other$valueAfter)) {
                        return false;
                    }

                    Object this$sensitiveType = this.getSensitiveType();
                    Object other$sensitiveType = other.getSensitiveType();
                    if (this$sensitiveType == null) {
                        if (other$sensitiveType != null) {
                            return false;
                        }
                    } else if (!this$sensitiveType.equals(other$sensitiveType)) {
                        return false;
                    }

                    return true;
                }
            }
        }

        protected boolean canEqual(Object other) {
            return other instanceof Diff;
        }

        public int hashCode() {
            int PRIME = 59;
            int result = 1;
            Object $columnCode = this.getColumnCode();
            result = result * 59 + ($columnCode == null ? 43 : $columnCode.hashCode());
            Object $valueBefore = this.getValueBefore();
            result = result * 59 + ($valueBefore == null ? 43 : $valueBefore.hashCode());
            Object $valueAfter = this.getValueAfter();
            result = result * 59 + ($valueAfter == null ? 43 : $valueAfter.hashCode());
            Object $sensitiveType = this.getSensitiveType();
            result = result * 59 + ($sensitiveType == null ? 43 : $sensitiveType.hashCode());
            return result;
        }

        public String toString() {
            return "BsOpLogCreateReqDto.Diff(columnCode=" + this.getColumnCode() + ", valueBefore=" + this.getValueBefore() + ", valueAfter=" + this.getValueAfter() + ", sensitiveType=" + this.getSensitiveType() + ")";
        }

        public Diff(String columnCode, String valueBefore, String valueAfter, String sensitiveType) {
            this.columnCode = columnCode;
            this.valueBefore = valueBefore;
            this.valueAfter = valueAfter;
            this.sensitiveType = sensitiveType;
        }

        public Diff() {
        }

        public static class DiffBuilder {
            private String columnCode;
            private String valueBefore;
            private String valueAfter;
            private String sensitiveType;

            DiffBuilder() {
            }

            @JsonProperty("column_code")
            public DiffBuilder columnCode(String columnCode) {
                this.columnCode = columnCode;
                return this;
            }

            @JsonProperty("value_before")
            public DiffBuilder valueBefore(String valueBefore) {
                this.valueBefore = valueBefore;
                return this;
            }

            @JsonProperty("value_after")
            public DiffBuilder valueAfter(String valueAfter) {
                this.valueAfter = valueAfter;
                return this;
            }

            @JsonProperty("sensitive_type")
            public DiffBuilder sensitiveType(String sensitiveType) {
                this.sensitiveType = sensitiveType;
                return this;
            }

            public Diff build() {
                return new Diff(this.columnCode, this.valueBefore, this.valueAfter, this.sensitiveType);
            }

            public String toString() {
                return "BsOpLogCreateReqDto.Diff.DiffBuilder(columnCode=" + this.columnCode + ", valueBefore=" + this.valueBefore + ", valueAfter=" + this.valueAfter + ", sensitiveType=" + this.sensitiveType + ")";
            }
        }
    }
}

