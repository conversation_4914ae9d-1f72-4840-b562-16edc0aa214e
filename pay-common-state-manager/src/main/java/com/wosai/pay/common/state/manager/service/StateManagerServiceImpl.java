package com.wosai.pay.common.state.manager.service;

import com.wosai.pay.common.base.util.JsonUtil;
import com.wosai.pay.common.data.Criteria;
import com.wosai.pay.common.state.manager.constant.StateManagerConstant;
import com.wosai.pay.common.state.manager.dao.StateDao;
import com.wosai.pay.common.state.manager.request.*;
import com.wosai.pay.common.state.manager.domain.AbstractStateDO;
import com.wosai.pay.common.state.manager.exception.StateManageBizException;
import com.wosai.pay.common.state.manager.result.StateConfigurationResult;
import com.wosai.pay.common.state.manager.result.StateManagerResult;
import com.wosai.pay.common.state.manager.registry.StateManagerProcessor;
import com.wosai.pay.common.state.manager.registry.StateManagerProcessorRegistry;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;


import java.util.*;
import java.util.stream.Collectors;


/**
 * 状态服务实现类
 * 实现了状态的查询和更新逻辑，支持不同实体类型的自定义处理
 */
public class StateManagerServiceImpl implements StateManagerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StateManagerServiceImpl.class);

    private final StateDao stateDao;
    private final KafkaTemplate<String, Object> stateManagerKafkaTemplate;

    private RpcBusinessOpLogService businessOpLogService;

    public StateManagerServiceImpl(StateDao stateDao, KafkaTemplate<String, Object> stateManagerKafkaTemplate) {
        this.stateDao = stateDao;
        this.stateManagerKafkaTemplate = stateManagerKafkaTemplate;
    }

    public StateManagerServiceImpl(StateDao stateDao, KafkaTemplate<String, Object> stateManagerKafkaTemplate, KafkaTemplate<String, Object> stateManagerKafkaTemplate1, RpcBusinessOpLogService businessOpLogService) {
        this.stateDao = stateDao;
        this.stateManagerKafkaTemplate = stateManagerKafkaTemplate1;
        this.businessOpLogService = businessOpLogService;
    }


    /**
     * 注册一个状态处理器
     *
     * @param processor 要注册的处理器
     * @return 注册是否成功
     */
    public boolean registerProcessor(StateManagerProcessor<?,?, ?> processor) {
        return StateManagerProcessorRegistry.register(processor);
    }


    @Override
    @SuppressWarnings("unchecked")
    public <T extends AbstractEntity, R extends StateManagerResult> R queryState(StateManagerRequest<T> stateManagerRequest) {
        String business = stateManagerRequest.getBusiness();

        StateManagerConfig.validBizAndType(business, null);
        List<StateConfigurationResult.SubStateConfiguration> stateConfigurationList = StateManagerConfig.getStatesList(business);

        T entity = stateManagerRequest.getEntity();
        String entityType = entity.getEntityType();
        // 获取实体类型对应的处理器
        StateManagerProcessor<AbstractEntity,AbstractStateDO, StateManagerResult> processor = StateManagerProcessorRegistry.getProcessor(entityType);
        if (processor == null) {
            throw new IllegalStateException("未找到实体类型[" + entityType + "]对应的处理器");
        }



        //初始化数据库DO
        AbstractStateDO abstractStateDO = initAndGetStateByBizIdAndEntity(business, entity, processor);

        // 创建返回结果
        R result = (R) processor.createResultInstance(entity);

        // 设置基本状态信息
        result.setState(abstractStateDO.getState());

        // 设置子状态列表
        List<StateManagerResult.SubState> subStateList = new ArrayList<>();
        Map<Integer, Boolean> subStates = abstractStateDO.getSubStates();

        if (stateConfigurationList != null) {
            for (StateConfigurationResult.SubStateConfiguration stateConfiguration : stateConfigurationList) {
                StateManagerResult.SubState subState = new StateManagerResult.SubState();
                Integer subStateType = stateConfiguration.getSubStateType();
                subState.setType(subStateType);
                subState.setDesc(stateConfiguration.getDescription());
                Boolean value = subStates.get(subStateType);
                subState.setValue(value != null ? value : true);
                subStateList.add(subState);
            }
        }

        result.setSubStateList(subStateList);

        return result;
    }


    @Override
    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public <T extends AbstractEntity, R extends StateManagerResult> Boolean changeState(StateManagerRequest<T> stateManagerRequest, OperationLogRequest operationLogRequest) {
        // 验证业务类型和状态类型
        String businessType = stateManagerRequest.getBusiness();
        Integer subStateType = stateManagerRequest.getSubStateType();
        StateManagerConfig.validBizAndType(businessType, subStateType);

        T entity = stateManagerRequest.getEntity();
        //查询变更前状态
        @SuppressWarnings("unchecked")
        R oldStateResult = (R) queryState(stateManagerRequest);

        // 获取实体类型对应的处理器
        String entityType = entity.getEntityType();
        StateManagerProcessor<AbstractEntity,AbstractStateDO, StateManagerResult> processor = StateManagerProcessorRegistry.getProcessor(entityType);
        if (processor == null) {
            throw new IllegalStateException("未找到实体类型[" + entityType + "]对应的处理器");
        }

        // 初始化或获取状态实体
        AbstractStateDO abstractStateDO = initAndGetStateByBizIdAndEntity(stateManagerRequest.getBusiness(), stateManagerRequest.getEntity(), processor);
        String originalSubState = abstractStateDO.getSubStatesBits();
        // 更新子状态
        Map<Integer, Boolean> subStates = abstractStateDO.getSubStates();
        if (subStates == null) {
            subStates = new HashMap<>();
            abstractStateDO.setSubStates(subStates);
        }
        subStates.put(subStateType, stateManagerRequest.getEnabled());
        abstractStateDO.setSubStates(subStates);
        // 更新总状态和其他信息
        abstractStateDO.setRemark(stateManagerRequest.getRemark());
        abstractStateDO.setState(StateManagerConstant.ALL_ENABLED_STATE_BITS.equals(abstractStateDO.getSubStatesBits()));
        if (originalSubState != null && originalSubState.equals(abstractStateDO.getSubStatesBits())) {
            return Boolean.TRUE;
        }
        // 更新状态
        stateDao.update(abstractStateDO);

        //查询变更后状态
        @SuppressWarnings("unchecked")
        R curStateResult = (R) queryState(stateManagerRequest);

        // 后置处理
        processor.afterStateChange(entity, oldStateResult, curStateResult, operationLogRequest);

        String bodyStr = null;
        try {
            bodyStr = JsonUtil.objectToJsonString(curStateResult);
            ListenableFuture<SendResult<String, Object>> result = stateManagerKafkaTemplate
                    .send("events_pay_fee_rate_activity_sync", "key", bodyStr);
            final String finalBodyStr = bodyStr;
            result.addCallback(new ListenableFutureCallback<SendResult<String, Object>>() {
                @Override
                public void onFailure(Throwable ex) {
                    LOGGER.error("发送消息失败. topic:{}, brokerAddress:{}, body:{}",
                           "", "", finalBodyStr, ex);
                }

                @Override
                public void onSuccess(SendResult<String, Object> result) {
                    LOGGER.info("发送成功 topic:{}", "");
                }
            });
        } catch (Exception e) {
            LOGGER.error("写入kafka失败. topic:{},brokerAddress:{},body:{}",
                   "","", bodyStr, e);
            throw new StateManageBizException("写入kafka失败.", e);
        }

        if (operationLogRequest!=null){
            sendProviderMchParamsDisableReasonChangeBusinessLog(entity, oldStateResult, curStateResult, operationLogRequest);
        }
        return Boolean.TRUE;
    }


    private <T extends AbstractEntity, E extends AbstractStateDO> AbstractStateDO initAndGetStateByBizIdAndEntity(String business, T entity, StateManagerProcessor processor) {
        AbstractStateDO abstractStateDO;
        Criteria criteria = processor.generateCriteria(entity);
        if (criteria == null) {
            throw new StateManageBizException("generateCriteria return is null");
        }
        criteria.with(StateManagerConstant.BUSINESS).is(business);
        abstractStateDO = stateDao.filter(criteria).fetchOne();
        if (abstractStateDO == null) {
            try {
                E newEntity = (E) processor.buildStateDO(entity);
                newEntity.setBusiness(business);
                newEntity.setState(Boolean.TRUE);
                newEntity.setSubStates(null);
                stateDao.insert(newEntity);
            } catch (DuplicateKeyException e) {
                LOGGER.debug("DuplicateKeyException");
            }
            abstractStateDO = stateDao.filter(criteria).fetchOne();
        }
        return abstractStateDO;
    }


    private <T extends AbstractEntity, R extends StateManagerResult> void sendProviderMchParamsDisableReasonChangeBusinessLog(T entity, R oldStateResult, R curStateResult, OperationLogRequest operationLogRequest) {
        ValidList<BsOpLogCreateReqDto.Diff> validList = new ValidList<>();
        BsOpLogCreateReqDto.Diff disableStatusDiff = new BsOpLogCreateReqDto.Diff();
        disableStatusDiff.setColumnCode(String.format("%s#%s", StateManagerConstant.STATE_MANAGER_PARAMS, StateManagerConstant.STATUS));
        disableStatusDiff.setValueBefore(oldStateResult.getState() ? "启用" : "禁用");
        disableStatusDiff.setValueAfter(curStateResult.getState() ? "启用" : "禁用");
        validList.add(disableStatusDiff);

        BsOpLogCreateReqDto.Diff disableReasonDiff = new BsOpLogCreateReqDto.Diff();
        disableReasonDiff.setColumnCode(String.format("%s#%s", StateManagerConstant.STATE_MANAGER_PARAMS, StateManagerConstant.EXTRA));


        disableReasonDiff.setValueBefore(JsonUtil.objectToJsonString(getFailedDescriptions(oldStateResult)));
        disableReasonDiff.setValueAfter(JsonUtil.objectToJsonString(getFailedDescriptions(curStateResult)));
        validList.add(disableReasonDiff);

        BsOpLogCreateReqDto reqDto = BsOpLogCreateReqDto.builder()
                .logTemplateCode(StringUtils.isBlank(operationLogRequest.getSceneTemplateCode()) ? StateManagerConstant.LOG_SCENE_TEMPLATE_CODE : operationLogRequest.getSceneTemplateCode())
                .rootObjectId(entity.getEntityType())
                .opObjectId(entity.getEntityType())
                .diffList(validList)
                .opUserId(operationLogRequest.getOperatorUserId())
                .opUserName(operationLogRequest.getOperatorUserName())
                .platformCode(operationLogRequest.getPlatformCode())
                .build();
        doSendBusinessLogStash(reqDto);
    }
    /**
     * 获取所有未通过(value=false)子状态的描述列表
     *
     * @param stateResult 包含子状态的对象
     * @return 未通过子状态的描述列表
     */
    public  < R extends StateManagerResult> List<String> getFailedDescriptions(  R stateResult) {
       return stateResult.getSubStateList().stream()
                .filter(x -> !x.getValue())   // 过滤 value 为 false 的元素
                .map(StateManagerResult.SubState::getDesc)  // 提取 desc 字段
                .collect(Collectors.toList());
    }


    private void doSendBusinessLogStash(BsOpLogCreateReqDto bsOpLogCreateReqDto) {
        LOGGER.debug("doSendBusinessLogStash,[商户日志]>>>>入参:{}", JsonUtil.objectToJsonString(bsOpLogCreateReqDto));
        businessOpLogService.createBusinessLogForAsync(bsOpLogCreateReqDto);
    }


}
