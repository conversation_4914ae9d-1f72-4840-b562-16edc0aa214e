package com.wosai.pay.common.state.manager.request;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import org.reflections.Reflections;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;


@Data
public abstract class AbstractEntity {

    public static final Logger logger = LoggerFactory.getLogger(AbstractEntity.class);

    private static final Map<String, Class<? extends AbstractEntity>> typeId2Class = new HashMap<>();

    private static volatile boolean initialized = false;

    public static void registerType(String entityType, Class<? extends AbstractEntity> clazz) {
        typeId2Class.put(entityType, clazz);
    }

    // 添加自定义反序列化器
    public static class AbstractStateObjectDeserializer
            extends JsonDeserializer<AbstractEntity> {

        @Override
        public AbstractEntity deserialize(
                JsonParser parser,
                DeserializationContext context
        ) throws IOException {

            // 解析为JSON树节点
            JsonNode node = parser.getCodec().readTree(parser);

            // 验证必须包含entityType字段
            if (!node.has("entityType")) {
                throw new IllegalStateException(
                        "JSON数据缺少必需的entityType字段");
            }

            String entityType = node.get("entityType").asText();

            // 获取目标类
            Class<? extends AbstractEntity> targetClass = AbstractEntity.classFor(entityType);
            if (targetClass == null) {
                throw new IllegalArgumentException(
                        "未知的对象类型: " + entityType);
            }

            // 转换为具体类型
            return parser.getCodec().treeToValue(node, targetClass);
        }
    }

    public static Class<? extends AbstractEntity> classFor(String entityType) {
        if (!initialized) {
            synchronized (AbstractEntity.class) {
                if (!initialized) {
                    initialize();
                    initialized = true;
                }
            }
        }
        return typeId2Class.get(String.format("%s", entityType));
    }

    private static void initialize() {
        // 获取所有classpath下的包
        //todo 这个地方有问题
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        Reflections reflections = new Reflections(new ConfigurationBuilder()
                .setUrls(ClasspathHelper.forClassLoader(classLoader))
                .setScanners(new SubTypesScanner())
                .addClassLoader(classLoader));

//        Reflections reflections = new Reflections(AbstractEntity.class.getPackage().getName());
        Set<Class<? extends AbstractEntity>> subTypes = reflections.getSubTypesOf(AbstractEntity.class);
        for (Class<? extends AbstractEntity> type : subTypes) {
            if (!Modifier.isAbstract(type.getModifiers())) {
                try {
                    type.newInstance();
                } catch (InstantiationException | IllegalAccessException e) {
                    logger.error("failed to load event type {}", type.getName(), e);
                }
            }
        }
    }



    private String entityType;

    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }
}
