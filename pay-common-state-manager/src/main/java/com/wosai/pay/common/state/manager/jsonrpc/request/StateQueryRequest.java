package com.wosai.pay.common.state.manager.jsonrpc.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.pay.common.state.manager.request.AbstractEntity;
import lombok.Data;

/**
 * 状态查询请求 DTO
 *
 * <AUTHOR>
 * @since 1.2.7-SNAPSHOT
 */
@Data
public class StateQueryRequest {

    /**
     * 业务类型
     * 例如: "settlement", "payment"
     */
    @JsonProperty("business")
    private String business;

    /**
     * 实体数据
     * 包含查询所需的实体信息，如 provider, providerMchId 等
     */
    @JsonProperty("entity")
    private AbstractEntity entity;


}
